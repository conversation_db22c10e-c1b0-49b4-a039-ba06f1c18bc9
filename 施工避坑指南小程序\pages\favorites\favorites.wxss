/* pages/favorites/favorites.wxss */

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  padding: 48rpx 32rpx;
  text-align: center;
  margin-bottom: 32rpx;
}

.logo .title {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 16rpx;
}

.logo .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  line-height: 1.4;
}

.header-stats {
  display: flex;
  justify-content: center;
  gap: 64rpx;
  margin-top: 32rpx;
}

.header-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 搜索栏 */
.search-section {
  margin-bottom: 24rpx;
  padding: 0 32rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #999;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 28rpx;
  color: #999;
  padding: 8rpx;
}

/* 收藏列表 */
.favorites-section {
  padding: 0 32rpx;
}

.episode-list {
  margin-top: 24rpx;
}

.episode-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.episode-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.episode-number {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
  min-width: 120rpx;
  text-align: center;
}

.episode-content {
  flex: 1;
}

.episode-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.episode-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.episode-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.category {
  font-size: 22rpx;
  color: #1877f2;
  background: rgba(24, 119, 242, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.favorite-time {
  font-size: 22rpx;
  color: #999;
}

.episode-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.remove-favorite {
  font-size: 32rpx;
  padding: 8rpx;
  color: #dc3545;
}

.episode-arrow {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 128rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-actions {
  display: flex;
  gap: 24rpx;
}

/* 搜索无结果 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 96rpx 32rpx;
  text-align: center;
}

.no-results-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.no-results-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.no-results-desc {
  font-size: 24rpx;
  color: #666;
}