
/* pages/episode/episode.wxss */

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.logo .title {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.logo .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  line-height: 1.4;
}

.episode-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.nav-item:active {
  background: rgba(255, 255, 255, 0.3);
}

.nav-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.nav-text {
  font-size: 24rpx;
}

.episode-indicator {
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}

/* 概览区域 */
.overview-section {
  margin-bottom: 32rpx;
}

.overview-card {
  padding: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 24rpx;
}

.key-points {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.point-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.point-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.point-text {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

/* 流程区域 */
.content-section {
  margin-bottom: 32rpx;
}

.process-flow {
  margin-top: 24rpx;
}

.flow-step {
  display: flex;
  margin-bottom: 32rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

/* 标准对比表格 */
.standards-section {
  margin-bottom: 32rpx;
}

.standards-table {
  padding: 0;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.header-cell {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #e9ecef;
}

.header-cell:last-child {
  border-right: none;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 20rpx 16rpx;
  font-size: 22rpx;
  color: #666;
  text-align: center;
  border-right: 1rpx solid #f0f0f0;
  line-height: 1.4;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.standard {
  color: #28a745;
  font-weight: 500;
}

.table-cell.problem {
  color: #dc3545;
}

/* 环境要求 */
.environment-section {
  margin-bottom: 32rpx;
}

.environment-card {
  padding: 32rpx;
}

.env-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.env-item:last-child {
  margin-bottom: 0;
}

.env-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
  min-width: 120rpx;
}

.env-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 常见瑕疵 */
.defects-section {
  margin-bottom: 32rpx;
}

.defects-list {
  margin-top: 24rpx;
}

.defect-item {
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-left: 6rpx solid #ffc107;
}

.defect-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.defect-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.defect-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.defect-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 12rpx;
}

.defect-solution {
  font-size: 24rpx;
  color: #28a745;
  display: block;
}

/* 检查清单 */
.checklist-section {
  margin-bottom: 32rpx;
}

.checklist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.complete-all-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 8rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 400;
  transition: all 0.3s ease;
  flex-shrink: 0;
  width: auto;
  min-width: auto;
}

.complete-all-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.complete-all-btn .btn-text {
  font-size: 18rpx;
}

.checklist-card {
  padding: 32rpx;
}

.checklist-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.checklist-item:active {
  background: #e9ecef;
}

.checkbox {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #28a745;
  border-color: #28a745;
}

.checkbox-icon {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}

.checklist-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.progress-bar {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #e9ecef;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.progress-track {
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 专家建议 */
.advice-section {
  margin-bottom: 32rpx;
}

.advice-card {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 6rpx solid #667eea;
}

.advice-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 操作按钮 */
.action-section {
  margin-bottom: 32rpx;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

/* 导航按钮 */
.navigation-section {
  margin-bottom: 48rpx;
}

.nav-buttons {
  display: flex;
  gap: 24rpx;
}

.nav-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 24rpx;
  background: white;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.nav-btn:active {
  background: #f8f9fa;
}

.nav-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.nav-btn-primary:active {
  opacity: 0.8;
}

.nav-btn-icon {
  font-size: 32rpx;
  margin: 0 8rpx;
}

.nav-btn-text {
  font-weight: 500;
}

